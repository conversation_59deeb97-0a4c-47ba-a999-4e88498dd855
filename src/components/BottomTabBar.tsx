import {BottomTabParamList} from '@/navigation/types';
import React, {useCallback} from 'react';
import {StyleProp, StyleSheet, View, ViewStyle} from 'react-native';
import {
  ArrowsRightLeftIcon,
  BoltIcon,
  CalculatorIcon,
  EllipsisHorizontalIcon,
  HomeIcon,
  NewspaperIcon,
} from 'react-native-heroicons/outline';
import {
  ArrowsRightLeftIcon as ArrowsRightLeftIconSolid,
  BoltIcon as BoltIconSolid,
  CalculatorIcon as CalculatorIconSolid,
  EllipsisHorizontalIcon as EllipsisHorizontalIconSolid,
  HomeIcon as HomeIconSolid,
  NewspaperIcon as NewspaperIconSolid,
} from 'react-native-heroicons/solid';
import {useSafeAreaFrame, useSafeAreaInsets} from 'react-native-safe-area-context';
import {useTheme} from 'styled-components/native';

import {AssetifyWhite} from '@/assets/index';
import Assetify from '@/assets/nav/homeFilled.svg';
import GlobalStyles from '@/constants/GlobalStyles';
import {baseColors} from '@/styles/colors';
import {ITheme} from '@/styles/themes';
import {vibrate} from '@/utils';
import BottomTabIcon from './BottomTabIcon';
import IconButton from './IconButton';

const ICON_SIZE = 28;

// prettier-ignore
const createIcons = (theme: ITheme) => ({
  Home: (isActive: boolean) =>
    isActive ? (
      <HomeIconSolid size={ICON_SIZE} color={theme.colors.primary} />
    ) : (
      <HomeIcon size={ICON_SIZE} color={theme.colors.secondary} />
    ),
  Lightning: (isActive: boolean) =>
    isActive ? (
      <BoltIconSolid size={ICON_SIZE} color={theme.colors.primary} />
    ) : (
      <BoltIcon size={ICON_SIZE} color={theme.colors.secondary} />
    ),
  Exchange: (isActive: boolean) =>
    isActive ? (
      <ArrowsRightLeftIconSolid size={ICON_SIZE} color={theme.colors.primary} />
    ) : (
      <ArrowsRightLeftIcon size={ICON_SIZE} color={theme.colors.secondary} />
    ),
  More: (isActive: boolean) =>
    isActive ? (
      <EllipsisHorizontalIconSolid size={33} color={theme.colors.primary} />
    ) : (
      <EllipsisHorizontalIcon size={33} color={theme.colors.secondary} />
    ),
  Calculator: (isActive: boolean) =>
    isActive ? (
      <CalculatorIconSolid size={ICON_SIZE} color={theme.colors.primary} />
    ) : (
      <CalculatorIcon size={ICON_SIZE} color={theme.colors.secondary} />
    ),
  News: (isActive: boolean) =>
    isActive ? (
      <NewspaperIconSolid size={ICON_SIZE} color={theme.colors.primary} />
    ) : (
      <NewspaperIcon size={ICON_SIZE} color={theme.colors.secondary} />
    ),
});

/* ========================================================================== */
/*                                     UI                                     */
/* ========================================================================== */

type Props = {
  state: any;
  descriptors: any;
  navigation: any;
  isLoggedIn: boolean;
};

const BottomTabBar = ({state, navigation, isLoggedIn}: Props) => {
  const theme = useTheme() as ITheme;

  const {height} = useSafeAreaFrame();
  const insets = useSafeAreaInsets();

  const selectedTab = state.routes[state.index].name;
  const icons = createIcons(theme);

  const handleTabPress = useCallback(
    (tabName: keyof BottomTabParamList) => {
      // logAnalyticsEventForTab(tabName);

      if (selectedTab === tabName) {
        const currentRoute = state.routes.find((route) => route.name === tabName);
        if (currentRoute?.state?.routes?.length > 1) {
          // Pop to the first screen in the stack
          navigation.popToTop();
        }
      }

      vibrate();
      navigation.navigate(tabName);
    },
    [selectedTab, state.routes, navigation],
  );

  const handleCBPress = () => {
    vibrate();
    navigation.navigate('Loan');
  };

  const renderTab = (
    tabName: keyof BottomTabParamList,
    Icon: React.ReactElement,
    style?: StyleProp<ViewStyle>,
  ) => (
    <BottomTabIcon
      IconComponent={Icon}
      onPress={() => handleTabPress(tabName)}
      style={style}
    />
  );

  const tabBarStyle = {
    height: isLoggedIn
      ? (height + insets.bottom + 30) / 10
      : (height + insets.bottom) / 10,
    // Add padding for devices with home indicators
    paddingBottom: insets.bottom > 0 ? 22 : 0,
    borderTopColor: 'transparent',
  };

  const styles = createStyles(theme);

  return (
    <>
      {!isLoggedIn ? (
        <View style={[styles.container, tabBarStyle]}>
          {renderTab('Onboarding', icons.Home(selectedTab === 'Onboarding'))}

          {renderTab('Calculator', icons.Calculator(selectedTab === 'Calculator'))}

          {renderTab('News', icons.News(selectedTab === 'News'))}
        </View>
      ) : (
        <View style={[styles.container, tabBarStyle]}>
          <View style={styles.tabsContainer}>
            {renderTab('Wallet', icons.Home(selectedTab === 'Wallet'))}

            {renderTab('Lightning', icons.Lightning(selectedTab === 'Lightning'))}

            <View style={styles.centralButtonContainer}>
              <IconButton onPress={handleCBPress} style={styles.centralButton}>
                {theme.id === 'dark' ? (
                  <AssetifyWhite
                    width={30}
                    height={30}
                    style={{marginLeft: 2.5, marginBottom: 3}}
                  />
                ) : (
                  <Assetify
                    width={30}
                    height={30}
                    style={{marginLeft: 2.5, marginBottom: 3}}
                  />
                )}
              </IconButton>
            </View>

            {renderTab('Exchange', icons.Exchange(selectedTab === 'Exchange'))}

            {renderTab('More', icons.More(selectedTab === 'More'))}
          </View>
        </View>
      )}
    </>
  );
};

export default BottomTabBar;

const createStyles = (currentTheme: ITheme) =>
  StyleSheet.create({
    container: {
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      alignItems: 'center',
      backgroundColor: currentTheme.colors.surfaceBackground,
      borderTopColor: currentTheme.colors.surfaceBackground,
      borderTopWidth: 1,
      // Shadows
      elevation: 4,
      shadowColor: '#633c61',
      shadowOffset: {width: 0, height: 4},
      shadowOpacity: 0.2,
      shadowRadius: 4,
    },
    tabsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-evenly',
      alignItems: 'center',
      width: '100%',
      paddingHorizontal: 8,
      paddingVertical: 8,
      height: 70,
      marginRight: -8,
    },
    centralButtonContainer: {
      width: '20%',
      alignItems: 'center',
      justifyContent: 'center',
    },
    centralButton: {
      width: 54,
      height: 54,
      backgroundColor: currentTheme.colors.surfaceBackground,
      borderColor: baseColors.gray.gray700,
      borderWidth: 1,
      borderRadius: 27,
      justifyContent: 'center',
      alignItems: 'center',
      // Shadows
      elevation: 4,
      shadowColor: GlobalStyles.primary.primary700,
      shadowOffset: {width: 0, height: 2},
      shadowOpacity: 0.4,
      shadowRadius: 2,
    },
  });
