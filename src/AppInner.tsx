import {BottomSheetModalProvider} from '@gorhom/bottom-sheet';
import NetInfo from '@react-native-community/netinfo';
import React, {useEffect, useMemo, useState} from 'react';
import {StatusBar} from 'react-native';
import FlashMessage from 'react-native-flash-message';
import {KeyboardProvider} from 'react-native-keyboard-controller';
import {OneSignal} from 'react-native-onesignal';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {ThemeProvider} from 'styled-components/native';

import {ONESIGNAL_APP_ID} from '@env';
import {useCommon} from './hooks/redux';
import {NavigationProvider} from './navigation/providers/NavigationProvider';
import {getTheme} from './styles/themes';

export const AppInner = () => {
  const {appTheme} = useCommon();
  const currentTheme = useMemo(() => getTheme(appTheme), [appTheme]);

  const [isConnected, setIsConnected] = useState(true);

  useEffect(() => {
    const unsubscribe = NetInfo.addEventListener((state) => {
      setIsConnected(!!state.isConnected);
    });

    OneSignal.initialize(ONESIGNAL_APP_ID);
    OneSignal.Notifications.requestPermission(true);

    return () => unsubscribe();
  }, []);

  return (
    <ThemeProvider theme={currentTheme}>
      <SafeAreaProvider>
        <StatusBar
          barStyle={appTheme === 'dark' ? 'light-content' : 'dark-content'}
          backgroundColor={currentTheme.colors.surfaceBackground}
        />

        <BottomSheetModalProvider>
          <KeyboardProvider>
            <NavigationProvider isConnected={isConnected} />
          </KeyboardProvider>
        </BottomSheetModalProvider>

        <FlashMessage position="top" />
      </SafeAreaProvider>
    </ThemeProvider>
  );
};

export default AppInner;
