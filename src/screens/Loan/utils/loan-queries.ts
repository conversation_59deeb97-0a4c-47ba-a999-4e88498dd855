import {
  AuthenticationInstance,
  LoanInstance,
  LtvInstance,
} from '@/services/BackendServices';
import {store} from '@/storage/store';

export const getUserLoans = async (): Promise<any> => {
  const {data} = await LoanInstance.get<any>(`/loan`, {
    headers: {
      Authorization: `Bear<PERSON> ${store.getState().loan.accessToken}`,
    },
  });
  return data;
};

export const getUserActiveLoans = async (): Promise<any> => {
  const {data} = await LoanInstance.get<any>(`/loan?active`, {
    headers: {
      Authorization: `Bearer ${store.getState().loan.accessToken}`,
    },
  });
  return data;
};

export const activate2FA = async (email: string): Promise<any> => {
  const {data} = await AuthenticationInstance.post<any>('/user/otp/generate', {
    email: email.trim(),
  });
  return data;
};

export const getLatestLtv = async (loanId: string) => {
  const {data} = await LtvInstance.get<any>(`/ltv/latest/${loanId}`);
  return data;
};
