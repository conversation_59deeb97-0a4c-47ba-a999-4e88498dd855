import {useQuery} from '@tanstack/react-query';

import {getLatestLtv, getUserLoans} from './loan-queries';

export const useUserLoans = () => {
  const query = useQuery({
    queryKey: ['user-loans'],
    queryFn: getUserLoans,
  });
  return query;
};

export const useLoanHealth = (loanId) => {
  const query = useQuery({
    queryKey: ['latest-ltv', loanId],
    queryFn: () => getLatestLtv(loanId),
    enabled: !!loanId,
  });
  return query;
};
