import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React, {memo} from 'react';
import {useTranslation} from 'react-i18next';
import {Al<PERSON>, ScrollView, StyleSheet, Switch, View} from 'react-native';
import {
  ArrowRightOnRectangleIcon,
  EnvelopeIcon,
  LockClosedIcon,
  MoonIcon,
  SunIcon,
  TrashIcon,
} from 'react-native-heroicons/solid';
import {useTheme} from 'styled-components/native';

import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch} from '@/hooks/redux';
import {LoanSettingsParamList} from '@/navigation/types';
import {navigationRef} from '@/navigation/utils/navigation';
import SettingsItem from '@/screens/SettingsScreen/ui/SettingsItem';
import SettingsSection from '@/screens/SettingsScreen/ui/SettingsSection';
import {setAppTheme} from '@/storage/actions/common';
import {setAccessToken} from '@/storage/actions/loanActions';
import {baseColors} from '@/styles/colors';
import theme, {ITheme} from '@/styles/themes';

type LoanSettingsHomeNavigationProp = StackNavigationProp<
  LoanSettingsParamList,
  'LoanSettingsHome'
>;

const LoanSettingsHome = () => {
  const currentTheme = useTheme() as ITheme;
  const isDark = currentTheme.id === 'dark';

  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const navigation = useNavigation<LoanSettingsHomeNavigationProp>();

  const setThemeMode = (theme: 'light' | 'dark') => {
    dispatch(setAppTheme(theme));
  };

  const renderThemeSwitch = () => (
    <View style={styles.switchView}>
      <SunIcon
        size={20}
        fill={isDark ? GlobalStyles.gray.gray600 : baseColors.orange.orange500}
      />
      <Switch
        trackColor={{
          false: GlobalStyles.gray.gray600,
          true: baseColors.orange.orange500,
        }}
        thumbColor={currentTheme.colors.surfaceBackground}
        onValueChange={() => {
          setThemeMode(isDark ? 'light' : 'dark');
        }}
        value={isDark}
      />
      <MoonIcon
        size={20}
        fill={isDark ? theme.colors.brand.coral : GlobalStyles.gray.gray600}
      />
    </View>
  );

  const handleLogout = async () => {
    try {
      dispatch(setAccessToken(''));
      navigationRef.current?.reset({
        index: 0,
        routes: [{name: 'LoanOnboarding'}],
      });
    } catch (error) {
      console.error('[LoanSettingsHome] Error during logout:', error);
      navigationRef.current?.reset({
        index: 0,
        routes: [{name: 'LoanOnboarding'}],
      });
    }
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      t('settings.deleteAccountMessage'),
      'This will permanently delete your account and all loan data. This action cannot be undone.',
      [
        {
          text: t('settings.no'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: t('settings.yes'),
          onPress: () => {
            handleLogout();
          },
          style: 'destructive',
        },
      ],
      {cancelable: false},
    );
  };

  const handleLogoutConfirmation = () => {
    Alert.alert(
      t('settings.logoutMessage'),
      'You will be logged out of your loan account.',
      [
        {
          text: t('settings.no'),
          onPress: () => {},
          style: 'cancel',
        },
        {
          text: t('settings.yes'),
          onPress: () => {
            handleLogout();
          },
        },
      ],
      {cancelable: false},
    );
  };

  const styles = createStyles(currentTheme);

  return (
    <View style={styles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <SettingsSection title="Appearance">
            <SettingsItem
              title="Theme"
              leftIcon={<MoonIcon size={26} fill={currentTheme.colors.primary} />}
              rightElement={renderThemeSwitch()}
            />
          </SettingsSection>

          <SettingsSection title="Security">
            <SettingsItem
              title="Two-Factor Authentication (2FA)"
              onPress={() => {
                navigation.navigate('LoanTwoFactorAuth');
              }}
              leftIcon={<LockClosedIcon size={26} fill={currentTheme.colors.primary} />}
            />

            <SettingsItem
              title="Change Email"
              onPress={() => {
                navigation.navigate('LoanChangeEmail');
              }}
              leftIcon={<EnvelopeIcon size={26} fill={currentTheme.colors.primary} />}
            />

            <SettingsItem
              title="Change Password"
              onPress={() => {
                navigation.navigate('LoanChangePassword');
              }}
              leftIcon={<LockClosedIcon size={26} fill={currentTheme.colors.primary} />}
            />
          </SettingsSection>

          <SettingsSection title={'Account'} isLast>
            <SettingsItem
              title={'Logout'}
              onPress={handleLogoutConfirmation}
              leftIcon={
                <ArrowRightOnRectangleIcon size={26} fill={currentTheme.colors.primary} />
              }
            />

            <SettingsItem
              title={'Delete Account'}
              onPress={handleDeleteAccount}
              leftIcon={<TrashIcon size={26} fill={baseColors.error.error700} />}
            />
          </SettingsSection>
        </View>
      </ScrollView>
    </View>
  );
};

const createStyles = (currentTheme: ITheme) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: currentTheme.colors.surfaceBackground,
      flexDirection: 'column',
    },
    content: {
      width: '100%',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: theme.spacing.xl,
      paddingHorizontal: theme.spacing.lg,
    },
    accountItem: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: 16,
      paddingHorizontal: 19,
      marginBottom: 32,
      width: '100%',
    },
    accountIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: GlobalStyles.orange.orange500,
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: 16,
    },
    accountInfo: {
      flex: 1,
    },
    themeContainer: {
      paddingHorizontal: 19,
      paddingVertical: 16,
    },
    switchView: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      gap: 16,
    },
    accountTitle: {
      fontSize: 17,
      fontFamily: GlobalStyles.fonts.sfPro,
      fontWeight: '600',
      lineHeight: 22,
      color: currentTheme.colors.primary,
      marginBottom: 2,
    },
    accountSubtitle: {
      fontSize: 15,
      fontFamily: GlobalStyles.fonts.sfPro,
      fontWeight: '400',
      lineHeight: 20,
      color: currentTheme.colors.secondary,
    },
    valueText: {
      fontSize: 16,
      fontFamily: GlobalStyles.fonts.sfPro,
      fontWeight: '500',
      lineHeight: 19,
      color: currentTheme.colors.primary,
      marginHorizontal: 2,
      alignSelf: 'center',
    },
  });

export default memo(LoanSettingsHome);
