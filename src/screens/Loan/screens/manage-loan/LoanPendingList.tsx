import EmptyLoan from '@/assets/icons/emptyLoan.svg';
import MButton from '@/components/MButton';
import PendingLoanModal from '@/components/modals/PendingLoanModal';
import GlobalStyles from '@/constants/GlobalStyles';
import {useModals} from '@/hooks/useModals';
import {useNavigation} from '@react-navigation/native';
import React, {memo, useCallback, useMemo, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {useTheme} from 'styled-components/native';
import {ITheme} from '@/styles/themes';
import LoanCard from '../../components/LoanCard';
import {useUserLoans} from '../../utils/loan-hooks';
import {ILoanState, Loan} from '../../utils/loan-types';

const LoanPendingList = () => {
  const {data, isLoading, error, refetch} = useUserLoans();
  const navigation = useNavigation();
  const currentTheme = useTheme() as ITheme;

  const {refs, show} = useModals(['pendingLoan']);
  const [selectedLoan, setSelectedLoan] = useState<Loan | null>(null);

  const [refreshing, setRefreshing] = useState(false);
  const styles = createStyles(currentTheme);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const pendingLoans = useMemo(() => {
    if (!data) return [];

    const filtered = data.filter((loan: Loan) => {
      return (
        loan.state === ILoanState.PENDING_BANK_TERMS_APPROVAL ||
        loan.state === ILoanState.PENDING_WALLET_CREATION ||
        loan.state === ILoanState.PENDING_COLLATERAL_DEPOSIT_TRANSACTION ||
        loan.state === ILoanState.PENDING_COLLATERAL_DEPOSIT_CONFIRMATION ||
        loan.state === ILoanState.PENDING_COLLATERAL_WITHDRAWAL_APPROVAL ||
        loan.state === ILoanState.PENDING_COLLATERAL_WITHDRAWAL_TRANSACTION ||
        loan.state === ILoanState.PENDING_COLLATERAL_WITHDRAWAL_CONFIRMATION
      );
    });

    return filtered.sort((a: Loan, b: Loan) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }, [data]);

  const handleLoanPress = useCallback(
    (loanId: string) => {
      const loan = pendingLoans.find((loan) => loan._id === loanId);
      if (loan) {
        setSelectedLoan(loan);
        show('pendingLoan');
      }
    },
    [pendingLoans],
  );

  const handleApplyForLoan = useCallback(() => {
    navigation.navigate('LoanDetails');
  }, [navigation]);

  const renderLoanItem = ({item}: {item: Loan}) => {
    const showDepositAddress =
      item.state === ILoanState.PENDING_COLLATERAL_DEPOSIT_TRANSACTION;
    return (
      <LoanCard
        loan={item}
        showDepositAddress={showDepositAddress}
        onPress={handleLoanPress}
      />
    );
  };

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={currentTheme.colors.primary} />
        <Text style={[styles.loadingText, {color: currentTheme.colors.primary}]}>
          Loading pending loans...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Failed to load loans. Please try again.</Text>
      </View>
    );
  }

  if (pendingLoans.length === 0) {
    return (
      <ScrollView
        style={styles.root}
        contentContainerStyle={styles.centered}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={currentTheme.colors.refreshControl}
            colors={[currentTheme.colors.refreshControl]}
          />
        }
      >
        <EmptyLoan width={80} height={80} />

        <Text style={styles.emptyTitle}>No Pending Loans</Text>
        <Text style={styles.emptyText}>You don't have any pending loan applications</Text>

        <MButton
          onPress={handleApplyForLoan}
          text="Apply for a Loan"
          variant="secondary"
        />
      </ScrollView>
    );
  }

  return (
    <View style={styles.root}>
      <FlatList
        data={pendingLoans}
        keyExtractor={(item) => item._id}
        renderItem={renderLoanItem}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        initialNumToRender={5}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={currentTheme.colors.refreshControl}
            colors={[currentTheme.colors.refreshControl]}
          />
        }
      />

      <PendingLoanModal ref={refs.pendingLoan} loan={selectedLoan} />
    </View>
  );
};

const createStyles = (theme: ITheme) =>
  StyleSheet.create({
    root: {
      flex: 1,
      backgroundColor: theme.colors.surfaceBackground,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
      backgroundColor: theme.colors.surfaceBackground,
    },
    listContainer: {
      padding: 16,
      paddingBottom: 32,
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
      color: theme.colors.primary,
    },
    errorText: {
      fontSize: 16,
      color: GlobalStyles.error.error400,
      textAlign: 'center',
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.primary,
      marginTop: 16,
      marginBottom: 8,
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.secondary,
      textAlign: 'center',
      marginBottom: 24,
      lineHeight: 24,
    },
    applyButtonContainer: {},
    applyButtonText: {
      color: theme.colors.primary,
      fontSize: 18,
      fontWeight: '500',
      letterSpacing: 0.5,
    },
    pullToRefreshText: {
      fontSize: 14,
      color: theme.colors.primary,
      textAlign: 'center',
    },
  });

export default memo(LoanPendingList);
