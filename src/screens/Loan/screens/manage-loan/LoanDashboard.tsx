import React, {useCallback, useMemo, useState} from 'react';
import {
  ActivityIndicator,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import PieChart from 'react-native-pie-chart';

import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {formatNumber} from '@/utils/index';
import {useLoanHealth, useUserLoans} from '../../utils/loan-hooks';
import {Loan} from '../../utils/loan-types';
import {capitalize} from 'lodash';

const LoanDashboard = ({route}: any) => {
  const loanId = route.params.loanId;
  const [refreshing, setRefreshing] = useState(false);

  const {data: ltvData, refetch: refetchLtv} = useLoanHealth(loanId);
  const {data: loans, isLoading} = useUserLoans();

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      refetchLtv();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refetchLtv]);

  const chartData = useMemo(() => {
    const apiLtv = formatNumber(ltvData?.data?.ltv || 0, {decimals: 0});
    const riskPercentage = apiLtv || 57;

    const getRiskColor = (percentage: number) => {
      if (percentage <= 60) {
        return '#10B981';
      } else {
        const intensity = (percentage - 60) / 20;

        if (intensity <= 0.33) {
          return '#EF4444';
        } else if (intensity <= 0.66) {
          return '#DC2626';
        } else {
          return '#B91C1C';
        }
      }
    };

    const riskColor = getRiskColor(riskPercentage);

    const maxRisk = 100;
    const adjustedRiskPercentage = Math.min(riskPercentage, maxRisk);
    const remainingPercentage = maxRisk - adjustedRiskPercentage;

    const rawData = [
      {
        value: adjustedRiskPercentage,
        color: riskColor,
        label: 'Current Risk',
        displayValue: `${riskPercentage}%`,
      },
      {
        value: remainingPercentage,
        color: GlobalStyles.gray.gray600,
        label: 'Safe Zone',
        displayValue: `${remainingPercentage}%`,
      },
    ];

    return {
      series: rawData.map((item) => ({value: item.value, color: item.color})),
      labels: rawData.map((item) => item.label),
      values: rawData.map((item) => item.displayValue),
      colors: rawData.map((item) => item.color),
      riskPercentage,
      riskColor,
    };
  }, [ltvData]);

  const loan = useMemo(() => {
    if (!loans) return null;
    return loans.find((l: Loan) => l._id === loanId);
  }, [loans, loanId]);

  if (isLoading || !loan) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={GlobalStyles.primary.primary500} />
        <Text style={styles.loadingText}>Loading loan details...</Text>
      </View>
    );
  }

  const nextPaymentDate = new Date();
  nextPaymentDate.setDate(nextPaymentDate.getDate() + 30);

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          tintColor={GlobalStyles.primary.primary500}
          colors={[GlobalStyles.primary.primary500]}
        />
      }
    >
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Loan Health</Text>
        <View style={styles.chartWrapper}>
          <PieChart
            widthAndHeight={200}
            series={chartData.series}
            cover={{radius: 0.6, color: '#FFF'}}
          />

          <View style={styles.centerValueContainer}>
            <Text style={[styles.centerValue, {color: chartData.riskColor}]}>
              {chartData.values[0]}
            </Text>
            <Text style={styles.riskStatus}>
              {chartData.riskPercentage <= 60 ? 'SAFE' : 'HIGH RISK'}
            </Text>
          </View>
        </View>
      </View>

      {/* Real-time Market Data Section */}
      {ltvData?.data && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Collateral Monitoring</Text>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>
              Collateral Price ({ltvData.data.price_at_calculation?.asset})
            </Text>
            <Text style={styles.detailValue}>
              $
              {formatNumber(ltvData.data.price_at_calculation?.price || 0, {
                decimals: 2,
              })}{' '}
              {ltvData.data.price_at_calculation?.currency}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Current LTV (Real-time)</Text>
            <Text
              style={[styles.detailValue, ltvData.data.ltv > 80 && styles.warningText]}
            >
              {formatNumber(ltvData.data.ltv || 0, {decimals: 0})}%
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Last Updated</Text>
            <Text style={styles.detailValue}>
              {ltvData.data.ts
                ? new Date(ltvData.data.ts).toLocaleString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit',
                  })
                : 'N/A'}
            </Text>
          </View>
        </View>
      )}

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Loan Details</Text>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Borrowed Amount</Text>
          <Text style={styles.detailValue}>
            {formatNumber(loan.terms.fiat.amount, {decimals: 2})}{' '}
            {loan.terms.fiat.currency}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Interest Rate</Text>
          <Text style={styles.detailValue}>{capitalize(loan.terms.interest)}</Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Collateral</Text>
          <Text style={styles.detailValue}>
            {formatNumber(loan.terms.collateral.amount)} {loan.terms.collateral.currency}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Loan Term</Text>
          <Text style={styles.detailValue}>{loan.terms.term} months</Text>
        </View>
      </View>

      <View style={styles.actionContainer}>
        <MButton text="Add Collateral" />

        <MButton text="Repay Loan" />

        <MButton text="Request Support" />
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: GlobalStyles.base.white,
    paddingHorizontal: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: GlobalStyles.base.white,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: GlobalStyles.gray.gray700,
  },
  chartContainer: {
    marginVertical: 42,
    paddingHorizontal: 16,
  },
  chartTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: GlobalStyles.gray.gray900,
    textAlign: 'center',
    marginBottom: 24,
  },
  chartWrapper: {
    alignItems: 'center',
    position: 'relative',
  },
  centerValueContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  centerValue: {
    fontSize: 22,
    fontWeight: '700',
    color: GlobalStyles.gray.gray900,
    textAlign: 'center',
  },
  riskStatus: {
    fontSize: 10,
    fontWeight: '600',
    color: GlobalStyles.gray.gray800,
    textAlign: 'center',
    marginTop: 4,
    letterSpacing: 1,
  },
  section: {
    padding: 16,
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: GlobalStyles.gray.gray200,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: GlobalStyles.gray.gray900,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 16,
    color: GlobalStyles.gray.gray800,
  },
  detailValue: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
  },
  warningText: {
    color: '#F59E0B',
  },
  actionContainer: {
    width: '90%',
    alignSelf: 'center',
    marginBottom: 32,
  },
});

export default LoanDashboard;
