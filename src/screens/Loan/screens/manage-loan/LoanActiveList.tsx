import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import React, {memo, useCallback, useMemo, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {useTheme} from 'styled-components/native';

import EmptyLoan from '@/assets/icons/emptyLoan.svg';
import GlobalStyles from '@/constants/GlobalStyles';
import {LoanStackParamList} from '@/navigation/types';
import {ITheme} from '@/styles/themes';
import LoanCard from '../../components/LoanCard';
import {useUserLoans} from '../../utils/loan-hooks';
import {ILoanState, Loan} from '../../utils/loan-types';

const EmptyLoanSvg = () => <EmptyLoan width={80} height={80} />;

const LoanActiveList = () => {
  const navigation = useNavigation<StackNavigationProp<LoanStackParamList>>();
  const currentTheme = useTheme() as ITheme;

  const {data, isLoading, error, refetch} = useUserLoans();

  const [refreshing, setRefreshing] = useState(false);
  const styles = createStyles(currentTheme);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const activeLoans = useMemo(() => {
    if (!data) return [];

    // Filter active loans
    const filtered = data.filter((loan: Loan) => loan.state === ILoanState.ACTIVE);

    // Sort loans by createdAt date, newest first
    return filtered.sort((a: Loan, b: Loan) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }, [data]);

  const handleLoanPress = useCallback(
    (loanId: string) => {
      navigation.navigate('LoanDashboard', {loanId});
    },
    [navigation],
  );

  const renderLoanItem = ({item}: {item: Loan}) => {
    return <LoanCard loan={item} onPress={handleLoanPress} />;
  };

  if (isLoading) {
    return (
      <View style={styles.root}>
        <View style={styles.centered}>
          <ActivityIndicator size="large" color={currentTheme.colors.primary} />
          <Text style={styles.loadingText}>Loading active loans...</Text>
        </View>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.root}>
        <View style={styles.centered}>
          <Text style={styles.errorText}>Failed to load loans. Please try again.</Text>
        </View>
      </View>
    );
  }

  if (activeLoans.length === 0) {
    return (
      <View style={styles.root}>
        <ScrollView
          style={styles.root}
          contentContainerStyle={styles.centered}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={currentTheme.colors.refreshControl}
              colors={[currentTheme.colors.refreshControl]}
            />
          }
        >
          <View style={styles.centered}>
            <EmptyLoanSvg />
            <Text style={styles.emptyTitle}>No Active Loans</Text>
            <Text style={styles.emptyText}>You don't have any active loans.</Text>
          </View>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={styles.root}>
      <FlatList
        data={activeLoans}
        keyExtractor={(item) => item._id}
        renderItem={renderLoanItem}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        initialNumToRender={5}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={currentTheme.colors.refreshControl}
            colors={[currentTheme.colors.refreshControl]}
          />
        }
      />
    </View>
  );
};

const createStyles = (theme: ITheme) =>
  StyleSheet.create({
    root: {
      flex: 1,
      backgroundColor: theme.colors.surfaceBackground,
    },
    listContainer: {
      padding: 16,
      paddingBottom: 32,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
      color: theme.colors.text,
    },
    errorText: {
      fontSize: 16,
      color: GlobalStyles.error.error500,
      textAlign: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.secondary,
      textAlign: 'center',
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.primary,
      marginTop: 16,
      marginBottom: 8,
    },
    debugText: {
      marginTop: 16,
      fontSize: 14,
      color: theme.colors.primary,
      textDecorationLine: 'underline',
    },
  });

export default memo(LoanActiveList);
