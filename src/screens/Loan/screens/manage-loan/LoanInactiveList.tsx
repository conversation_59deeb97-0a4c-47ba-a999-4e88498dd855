import {memo, useCallback, useMemo, useState} from 'react';
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import {useTheme} from 'styled-components/native';

import EmptyLoan from '@/assets/icons/emptyLoan.svg';
import GlobalStyles from '@/constants/GlobalStyles';
import {ITheme} from '@/styles/themes';
import LoanCard from '../../components/LoanCard';
import {useUserLoans} from '../../utils/loan-hooks';
import {ILoanState, Loan} from '../../utils/loan-types';

const EmptyLoanSvg = () => <EmptyLoan width={80} height={80} />;

const LoanInactiveList = () => {
  const currentTheme = useTheme() as ITheme;

  const {data, isLoading, error, refetch} = useUserLoans();
  const [refreshing, setRefreshing] = useState(false);
  const styles = createStyles(currentTheme);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  }, [refetch]);

  const inactiveLoans = useMemo(() => {
    if (!data) return [];

    // Filter inactive loans
    const filtered = data.filter((loan: Loan) => {
      return (
        loan.state === ILoanState.COMPLETED ||
        loan.state === ILoanState.TERMINATED ||
        loan.state === ILoanState.REJECTED
      );
    });

    // Sort loans by createdAt date, newest first
    return filtered.sort((a: Loan, b: Loan) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }, [data]);

  const renderLoanItem = ({item}: {item: Loan}) => {
    return <LoanCard loan={item} />;
  };

  if (isLoading) {
    return (
      <View style={styles.centered}>
        <ActivityIndicator size="large" color={currentTheme.colors.primary} />
        <Text style={styles.loadingText}>Loading inactive loans...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centered}>
        <Text style={styles.errorText}>Failed to load loans. Please try again.</Text>
      </View>
    );
  }

  if (inactiveLoans.length === 0) {
    return (
      <View style={styles.centered}>
        <EmptyLoanSvg />
        <Text style={styles.emptyTitle}>No Inactive Loans</Text>
        <Text style={styles.emptyText}>You don't have any inactive loans.</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={inactiveLoans}
      style={styles.root}
      keyExtractor={(item) => item._id}
      renderItem={renderLoanItem}
      contentContainerStyle={styles.listContainer}
      showsVerticalScrollIndicator={false}
      initialNumToRender={5}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          tintColor={currentTheme.colors.refreshControl}
          colors={[currentTheme.colors.refreshControl]}
        />
      }
    />
  );
};

const createStyles = (theme: ITheme) =>
  StyleSheet.create({
    root: {
      backgroundColor: theme.colors.surfaceBackground,
    },
    listContainer: {
      padding: 16,
      paddingBottom: 32,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 24,
      backgroundColor: theme.colors.surfaceBackground,
    },
    loadingText: {
      marginTop: 12,
      fontSize: 16,
      color: theme.colors.primary,
    },
    errorText: {
      fontSize: 16,
      color: GlobalStyles.error.error500,
      textAlign: 'center',
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.secondary,
      textAlign: 'center',
    },
    emptyTitle: {
      fontSize: 20,
      fontWeight: '600',
      color: theme.colors.primary,
      marginTop: 16,
      marginBottom: 8,
    },
  });

export default memo(LoanInactiveList);
