import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs';
import React, {memo} from 'react';
import {useTheme} from 'styled-components/native';

import {UserLoansTopTabsParamList} from '@/navigation/types';
import LoanActiveList from '@/screens/Loan/screens/manage-loan/LoanActiveList';
import LoanInactiveList from '@/screens/Loan/screens/manage-loan/LoanInactiveList';
import LoanPendingList from '@/screens/Loan/screens/manage-loan/LoanPendingList';
import {ITheme} from '@/styles/themes';

const UserLoansTopTabs = createMaterialTopTabNavigator<UserLoansTopTabsParamList>();
const screenOptions = {
  tabBarLabelStyle: {
    fontSize: 14,
    fontWeight: '700',
  },
};

const UserLoansTopTabsNavigator: React.FC = () => {
  const theme = useTheme() as ITheme;

  return (
    <UserLoansTopTabs.Navigator
      initialRouteName="Active"
      style={{
        backgroundColor: theme.colors.surfaceBackground,
      }}
      screenOptions={{
        ...screenOptions,
        tabBarIndicatorContainerStyle: {
          backgroundColor: theme.colors.surfaceBackground,
        },
        tabBarLabelStyle: {
          color: theme.colors.primary,
        },
        tabBarIndicatorStyle: {
          backgroundColor: theme.colors.secondary,
        },
      }}
    >
      <UserLoansTopTabs.Screen name="Pending" component={LoanPendingList} />
      <UserLoansTopTabs.Screen name="Active" component={LoanActiveList} />
      <UserLoansTopTabs.Screen name="Inactive" component={LoanInactiveList} />
    </UserLoansTopTabs.Navigator>
  );
};

export default memo(UserLoansTopTabsNavigator);
